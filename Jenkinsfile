pipeline {
    agent any
    
    tools {
        jdk "JDK21"  // Make sure <PERSON><PERSON><PERSON><PERSON> is configured in <PERSON>
    }
    
    environment {
        PROJECT_NAME = "cenor-elisa-part-sync"
        AWS_PROFILE = "cenor"
        S3_BUCKET = "cenor-lambda"
        AWS_REGION = "eu-north-1"
    }
    
    stages {
        stage('Environment Check') {
            steps {
                sh '''
                    echo "=== Environment Check ==="
                    java -version
                    ./gradlew --version
                    aws --version
                '''
            }
        }
        
        stage('Clean') {
            steps {
                script {
                    echo "=== Cleaning previous builds ==="
                    sh './gradlew clean'
                }
            }
        }
        
        stage('Build') {
            steps {
                script {
                    def buildEnv = env.GIT_BRANCH == 'origin/main' ? 'prod' : 'dev'
                    
                    echo "=== Building JAR file for ${buildEnv.toUpperCase()} environment ==="
                    echo "Branch: ${env.GIT_BRANCH}"
                    echo "Build Environment: ${buildEnv}"
                    
                    sh "./gradlew shadowJar -DbuildEnv=${buildEnv}"
                    
                    def version = sh(script: "./gradlew -q versionName", returnStdout: true).trim()
                    def expectedJar = "${PROJECT_NAME}-${version}-${buildEnv}.jar"
                    
                    echo "Expected JAR: ${expectedJar}"
                    sh "ls -la build/libs/${expectedJar}"
                }
            }
            post {
                success {
                    echo "Build completed successfully"
                }
                failure {
                    error "Build failed - check the build logs"
                }
            }
        }
        
        // stage('Test') {
        //     steps {
        //         script {
        //             echo "=== Running tests ==="
        //             sh '''
        //                 ./gradlew test
        //             '''
        //         }
        //     }
        //     post {
        //         always {
        //             script {
        //                 if (fileExists('build/test-results/test/*.xml')) {
        //                     publishTestResults testResultsPattern: 'build/test-results/test/*.xml'
        //                 }
        //             }
        //         }
        //     }
        // }
        
        stage('Deploy') {
            steps {
                script {
                    if (env.GIT_BRANCH == 'origin/dev') {
                        def version = sh(script: "./gradlew -q versionName", returnStdout: true).trim()
                        def functionName = "${PROJECT_NAME}-uat"
                        def fileName = "${PROJECT_NAME}-${version}-dev.jar"
                        def sourcePath = "${workspace}/build/libs/${fileName}"

                        echo "=== Deploying to UAT Environment ==="
                        echo "Function Name: ${functionName}"
                        echo "Version: ${version}"
                        echo "File Name: ${fileName}"
                        echo "Source Path: ${sourcePath}"
                        
                        sh "aws s3 cp \"${sourcePath}\" s3://${S3_BUCKET}/${fileName} --profile ${AWS_PROFILE} --region ${AWS_REGION}"
                        sh "aws lambda update-function-code --function-name ${functionName} --s3-bucket ${S3_BUCKET} --s3-key ${fileName} --profile ${AWS_PROFILE} --region ${AWS_REGION}"
                        
                        echo "UAT deployment completed successfully"
                    } else if (env.GIT_BRANCH == 'origin/main') {
                        def version = sh(script: "./gradlew -q versionName", returnStdout: true).trim()
                        def functionName = "${PROJECT_NAME}-prod"
                        def fileName = "${PROJECT_NAME}-${version}-prod.jar"
                        def sourcePath = "${workspace}/build/libs/${fileName}"
                        
                        echo "=== Deploying to Production Environment ==="
                        echo "Function Name: ${functionName}"
                        echo "Version: ${version}"
                        echo "File Name: ${fileName}"
                        echo "Source Path: ${sourcePath}"
                        
                        sh "aws s3 cp \"${sourcePath}\" s3://${S3_BUCKET}/${fileName} --profile ${AWS_PROFILE} --region ${AWS_REGION}"
                        sh "aws lambda update-function-code --function-name ${functionName} --s3-bucket ${S3_BUCKET} --s3-key ${fileName} --profile ${AWS_PROFILE} --region ${AWS_REGION}"
                        
                        echo "Production deployment completed successfully"
                    } else {
                        echo "No deployment configured for branch: ${env.GIT_BRANCH}"
                    }
                }
            }
        }
        
        // stage('Post-Deployment Test') {
        //     when {
        //         anyOf {
        //             branch 'main'
        //             branch 'dev'
        //         }
        //     }
        //     steps {
        //         script {
        //             echo "=== Running post-deployment verification ==="
        //             
        //             def functionName = env.GIT_BRANCH == 'origin/main' ? "${PROJECT_NAME}" : "${PROJECT_NAME}-dev"
        //             
        //             sh """
        //                 echo "Testing function: ${functionName}"
        //                 aws lambda invoke --function-name ${functionName} --payload {} response.json --profile ${AWS_PROFILE} --region ${AWS_REGION}
        //                 cat response.json
        //             """
        //         }
        //     }
        // }
    }
    
} 
