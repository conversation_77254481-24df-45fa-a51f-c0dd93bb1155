#!/bin/bash

# Configuration variables
FUNCTION_NAME="cenor-elisa-part-sync"
ENVIRONMENT="uat"
AWS_PROFILE="cenor"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Output functions
print_header() {
    echo -e "\n${PURPLE}===========================================${NC}"
    echo -e "${PURPLE}  $1${NC}"
    echo -e "${PURPLE}===========================================${NC}\n"
}

print_step() {
    echo -e "\n${BLUE}>>> $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header "Deploying Lambda Function"
echo "AWS Function Name: $FUNCTION_NAME-$ENVIRONMENT"
echo "AWS Profile: $AWS_PROFILE"

print_step "Getting application version..."
APP_VERSION=$(./gradlew -q versionName | tail -1)
if [ -z "$APP_VERSION" ]; then
    print_error "Failed to get application version!"
    exit 1
fi
print_success "Application version: $APP_VERSION"

JAR_FILE="build/libs/${FUNCTION_NAME}-${APP_VERSION}-all.jar"

print_step "Cleaning previous builds..."
./gradlew clean

print_step "Building shadow JAR for Lambda..."
./gradlew shadowJar

print_step "Verifying JAR was created..."
if [ -f "$JAR_FILE" ]; then
    print_success "Shadow JAR created successfully:"
    ls -lh "$JAR_FILE"
else
    print_error "Shadow JAR not found: $JAR_FILE"
    print_warning "Available JARs in build/libs/:"
    ls -la build/libs/ || echo "No build/libs directory found"
    exit 1
fi

# echo "JAR contents preview:"
# jar tf "$JAR_FILE" | head -20

print_step "Deploying Lambda function..."

if [ ! -f "$JAR_FILE" ]; then
    print_error "JAR file not found: $JAR_FILE"
    exit 1
fi

print_step "Checking if function exists..."
if ! aws lambda get-function \
    --function-name "$FUNCTION_NAME-$ENVIRONMENT" \
    --profile "$AWS_PROFILE" \
    >/dev/null 2>&1; then
    print_error "Lambda function '$FUNCTION_NAME-$ENVIRONMENT' does not exist. Create it first."
    exit 1
fi

print_step "Updating function code..."
UPDATE_RESULT=$(aws lambda update-function-code \
    --function-name "$FUNCTION_NAME-$ENVIRONMENT" \
    --zip-file fileb://"$JAR_FILE" \
    --profile "$AWS_PROFILE" \
    --output json 2>&1)

if [ $? -eq 0 ]; then
    print_success "Function code updated successfully"
    
    VERSION=$(echo "$UPDATE_RESULT" | jq -r '.Version // "N/A"')
    CODE_SIZE=$(echo "$UPDATE_RESULT" | jq -r '.CodeSize // "N/A"')
    echo "  Lambda Version: $VERSION"
    echo "  Code Size: $CODE_SIZE bytes"
    echo "  App Version: $APP_VERSION"
    
    print_step "Waiting for function to be active..."
    aws lambda wait function-updated \
        --function-name "$FUNCTION_NAME-$ENVIRONMENT" \
        --profile "$AWS_PROFILE"
    
    if [ $? -eq 0 ]; then
        print_success "Function is ready for invocation"
    else
        print_warning "Function update may still be in progress"
    fi
else
    print_error "Failed to update function code"
    echo "$UPDATE_RESULT"
    exit 1
fi