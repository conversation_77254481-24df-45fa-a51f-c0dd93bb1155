#!/bin/bash

verify_java() {
    if ! command -v java &> /dev/null; then
        echo "Error: Java is not installed or not in PATH"
        exit 1
    fi

    java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')

    if [[ ! $java_version == 21.* ]]; then
        echo "Error: Java 21 is required, but found version $java_version"
        exit 1
    fi

    echo "Java 21 verified: $java_version"
}



# Verify Java 21 is installed
verify_java

echo "Building the project..."
./gradlew clean shadowJar

if [ $? -ne 0 ]; then
    echo "Build failed. Exiting."
    exit 1
fi

APP_VERSION=$(./gradlew -q versionName | tail -1)

# Start the local API with SAM
echo ""
echo "Starting local API with SAM..."
sam local start-api --template dev-template.yaml --env-vars dev-env.json --parameter-overrides appVersion=$APP_VERSION