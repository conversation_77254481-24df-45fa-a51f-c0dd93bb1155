package se.visionmate.exception;

/**
 * Exception thrown when no valid parts are found after validation.
 * Used to signal that XML processing should be skipped or handled differently
 * based on the environment.
 */
public class NoValidPartsException extends RuntimeException {
    
    private final int totalItems;
    
    public NoValidPartsException(int totalItems) {
        super(String.format("No valid parts found after validation. All %d items failed validation", totalItems));
        this.totalItems = totalItems;
    }
    
    public int getTotalItems() {
        return totalItems;
    }
    
    public int getValidParts() {
        return 0; // Always 0 when this exception is thrown
    }
    
    public int getInvalidParts() {
        return totalItems; // Same as totalItems since all items failed validation
    }
}


