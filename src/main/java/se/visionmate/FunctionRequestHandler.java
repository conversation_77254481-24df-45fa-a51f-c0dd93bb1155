package se.visionmate;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;

import io.micronaut.context.annotation.Value;
import io.micronaut.context.env.Environment;
import io.micronaut.function.aws.MicronautRequestHandler;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import se.visionmate.model.dto.InventoryRequestDto;
import se.visionmate.model.dto.InventoryNotificationDto;
import se.visionmate.model.dto.SftpResponseDto;
import se.visionmate.model.visma.InventoryItem;
import se.visionmate.service.EmailService;
import se.visionmate.service.RequestService;
import se.visionmate.service.SftpService;
import se.visionmate.service.VismaService;
import se.visionmate.service.XmlService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FunctionRequestHandler extends MicronautRequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {

    @Inject
    private Environment environment;

    @Value("${response.content-type:application/json}")
    private String responseContentType;

    @Inject
    private RequestService requestService;

    @Inject
    private VismaService vismaService;

    @Inject
    private XmlService xmlService;

    @Inject
    private SftpService sftpService;

    @Inject
    private EmailService emailService;

    @Override
    public APIGatewayProxyResponseEvent execute(APIGatewayProxyRequestEvent input) {
        APIGatewayProxyResponseEvent response = new APIGatewayProxyResponseEvent();

        // DEBUG: Log all environment variables while running locally
        if (environment.getActiveNames().contains("dev")) {
            log.info("=== DEBUG: ALL ENVIRONMENT VARIABLES ===");
            System.getenv().entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> log.info("ENV: {} = {}", entry.getKey(), entry.getValue()));
            log.info("=== END ENVIRONMENT VARIABLES ===");
        }

        try {
            log.info("Starting inventory sync process...");
            
            List<InventoryItem> inventoryItems;
            
            if (requestService.isNotificationPayload(input.getBody())) {
                log.info("Processing notification payload");
                List<InventoryNotificationDto> notifications = requestService.parseNotificationPayload(input.getBody());
                inventoryItems = vismaService.processNotifications(notifications);
            } else {
                InventoryRequestDto requestDto = requestService.parseRequestBody(input.getBody());
                
                if (requestDto.isSingleProductRequest()) {
                    log.info("Processing single product request: productNumber={}", requestDto.getInventoryNumber());
                    inventoryItems = vismaService.getInventoryItem(requestDto.getInventoryNumber());
                    log.info("Fetched single inventory item: {}", requestDto.getInventoryNumber());
                } else if (requestDto.isPaginationRequest()) {
                    log.info("Processing pagination request: pageSize={}, pageNumber={}", 
                    requestDto.getPageSize(), requestDto.getPageNumber());
                    inventoryItems = vismaService.getInventoryItems(requestDto.getPageSize(), requestDto.getPageNumber());
                    log.info("Fetched {} inventory items with pagination", inventoryItems.size());
                } else {
                    log.info("Processing default request - fetching all inventory items");
                    inventoryItems = vismaService.getInventoryItems();
                    log.info("Fetched {} inventory items", inventoryItems.size());
                }
            }

            log.info("Generating XML");
            String xml = xmlService.generateInventoryXml(inventoryItems);

            Map<String, String> headers = new HashMap<>();
            
            // Check if response should be XML instead of JSON with SFTP upload
            if ("application/xml".equalsIgnoreCase(responseContentType)) {
                headers.put("Content-Type", "application/xml");
                response.setHeaders(headers);
                response.setStatusCode(200);
                response.setBody(xml);
            } else {
                // Default behavior: upload via SFTP and return JSON response
                headers.put("Content-Type", "application/json");
                response.setHeaders(headers);

                try {
                    log.info("Uploading XML to SFTP via Lambda");
                    String fileName = "PartSync_" + System.currentTimeMillis() + ".xml";
                    SftpResponseDto sftpResponse = sftpService.uploadFile(fileName, xml);

                    response.setStatusCode(200);
                    response.setBody("{\"status\":\"" + sftpResponse.getStatus() + "\",\"message\":\"" + 
                                   sftpResponse.getMessage().replace("\"", "\\\"") + "\",\"filesProcessed\":" + 
                                   sftpResponse.getFilesProcessed() + ",\"successCount\":" + 
                                   sftpResponse.getSuccessCount() + ",\"errorCount\":" + 
                                   sftpResponse.getErrorCount() + "}");

                    log.info("Inventory sync and SFTP upload process completed successfully");
                } catch (Exception sftpError) {
                    log.error("Failed to upload XML via SFTP", sftpError);
                    emailService.sendErrorNotification("SFTP upload failed during inventory sync process", sftpError);
                    response.setStatusCode(500);
                    response.setBody("{\"error\":\"SFTP upload failed: " + sftpError.getMessage().replace("\"", "\\\"") + "\"}");
                }
            }

        } catch (Exception e) {
            log.error("Error in inventory sync process", e);
            emailService.sendErrorNotification("Critical error occurred in inventory sync process", e);
            response.setStatusCode(500);
            response.setBody("{\"error\":\"" + e.getMessage().replace("\"", "\\\"") + "\"}");
        }

        return response;
    }

}
