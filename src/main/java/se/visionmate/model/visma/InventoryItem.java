package se.visionmate.model.visma;

import io.micronaut.serde.annotation.Serdeable;
import lombok.Data;

import java.util.List;

@Data
@Serdeable
public class InventoryItem {
    private String inventoryNumber;
    private String description;
    private String baseUnit;
    private String note;
    private ItemClass itemClass;
    private LotSerialClass lotSerialClass;
    private List<CrossReference> crossReferences;

    @Data
    @Serdeable
    public static class ItemClass {
        private String id;
        private String description;
    }

    @Data
    @Serdeable
    public static class LotSerialClass {
        private String id;
        private String description;
    }

    @Data
    @Serdeable
    public static class CrossReference {
        private String alternateID;
        private String alternateType;
        private String description;
    }
}