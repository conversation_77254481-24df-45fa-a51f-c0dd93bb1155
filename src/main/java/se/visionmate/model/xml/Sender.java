package se.visionmate.model.xml;

import jakarta.xml.bind.annotation.*;
import lombok.Data;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Sender {
    
    @XmlElement(name = "LogicalId", namespace = "http://www.consafelogistics.com/astro/project")
    private String logicalId;
    
    @XmlElement(name = "Confirmation", namespace = "http://www.consafelogistics.com/astro/project")
    private String confirmation;
}