package se.visionmate.model.xml;

import jakarta.xml.bind.annotation.*;
import lombok.Data;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class DataArea {
    
    @XmlElement(name = "Sync", namespace = "http://www.consafelogistics.com/astro/project")
    private Sync sync;
    
    @XmlElement(name = "Part", namespace = "http://www.consafelogistics.com/astro/project")
    private List<Part> parts;
}