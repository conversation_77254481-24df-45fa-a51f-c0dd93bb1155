package se.visionmate.model.xml;

import io.micronaut.core.annotation.Introspected;
import jakarta.xml.bind.annotation.*;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Introspected
@XmlAccessorType(XmlAccessType.FIELD)
public class Part {

    @XmlElement(name = "PartId", namespace = "http://www.consafelogistics.com/astro/project")
    private PartId partId;

    @XmlElement(name = "EANCodeArea", namespace = "http://www.consafelogistics.com/astro/project")
    private EANCodeArea eanCodeArea;

    @XmlElement(name = "Description", namespace = "http://www.consafelogistics.com/astro/project")
    private String description;

    @Size(min = 1, max = 4, message = "Unit type must be between 1 and 4 characters")
    @XmlElement(name = "UnitType", namespace = "http://www.consafelogistics.com/astro/project")
    private String unitType;

    @XmlElement(name = "SerialNumberLevel", namespace = "http://www.consafelogistics.com/astro/project")
    private String serialNumberLevel;

    @XmlElement(name = "LotCreationCode", namespace = "http://www.consafelogistics.com/astro/project")
    private String lotCreationCode;

    @XmlElement(name = "ManufacturerProductCode", namespace = "http://www.consafelogistics.com/astro/project")
    private String manufacturerProductCode;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class PartId {

        @XmlElement(name = "Id", namespace = "http://www.consafelogistics.com/astro/project")
        private String id;

        @XmlElement(name = "Revision", namespace = "http://www.consafelogistics.com/astro/project")
        private String revision;

        @XmlElement(name = "Division", namespace = "http://www.consafelogistics.com/astro/project")
        private String division;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class EANCodeArea {

        @XmlElement(name = "SingleUnit", namespace = "http://www.consafelogistics.com/astro/project")
        private SingleUnit singleUnit;

        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class SingleUnit {

            @XmlElement(name = "EANCode", namespace = "http://www.consafelogistics.com/astro/project")
            private EANCode eanCode;

            @Data
            @XmlAccessorType(XmlAccessType.FIELD)
            public static class EANCode {

                @XmlElement(name = "EANCodeId", namespace = "http://www.consafelogistics.com/astro/project")
                private EANCodeId eanCodeId;

                @Data
                @XmlAccessorType(XmlAccessType.FIELD)
                public static class EANCodeId {

                    @XmlElement(name = "Id", namespace = "http://www.consafelogistics.com/astro/project")
                    private String id;

                    @XmlElement(name = "Type", namespace = "http://www.consafelogistics.com/astro/project")
                    private String type;
                }
            }
        }
    }
}
