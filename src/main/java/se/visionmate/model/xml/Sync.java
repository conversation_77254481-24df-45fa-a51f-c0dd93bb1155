package se.visionmate.model.xml;

import jakarta.xml.bind.annotation.*;
import lombok.Data;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Sync {
    
    @XmlElement(name = "ActionCriteria", namespace = "http://www.consafelogistics.com/astro/project")
    private ActionCriteria actionCriteria = new ActionCriteria();
    
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class ActionCriteria {
        
        @XmlAttribute(name = "action")
        private String action;
    }
}