package se.visionmate.model.xml;

import jakarta.xml.bind.annotation.*;
import lombok.Data;

@Data
@XmlRootElement(name = "PartSync", namespace = "http://www.consafelogistics.com/astro/project")
@XmlAccessorType(XmlAccessType.FIELD)
public class PartSync {
    
    @XmlAttribute(name = "version")
    private String version = "0100";
    
    @XmlElement(name = "ControlArea", namespace = "http://www.consafelogistics.com/astro/project")
    private ControlArea controlArea;
    
    @XmlElement(name = "DataArea", namespace = "http://www.consafelogistics.com/astro/project")
    private DataArea dataArea;
}