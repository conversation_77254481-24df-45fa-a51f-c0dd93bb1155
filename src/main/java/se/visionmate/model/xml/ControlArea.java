package se.visionmate.model.xml;

import jakarta.xml.bind.annotation.*;
import lombok.Data;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class ControlArea {
    
    @XmlElement(name = "Sender", namespace = "http://www.consafelogistics.com/astro/project")
    private Sender sender;
    
    @XmlElement(name = "CreationDateTime", namespace = "http://www.consafelogistics.com/astro/project")
    private String creationDateTime;
    
    @XmlElement(name = "ReferenceId", namespace = "http://www.consafelogistics.com/astro/project")
    private ReferenceId referenceId;
}