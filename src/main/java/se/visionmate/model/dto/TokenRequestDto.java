package se.visionmate.model.dto;

import io.micronaut.serde.annotation.Serdeable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Serdeable
public class TokenRequestDto {
    private String clientId;
    private String clientSecret;
    private String tenantId;
    private String scope;
}