package se.visionmate.model.dto;

import io.micronaut.serde.annotation.Serdeable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Serdeable
public class InventoryNotificationDto {
    private String notificationId;
    private String contextId;
    private String event;
    private String action;
    private String resourceId;
    private String resourceUri;
    private String relativeResourceUri;
    private Long timeStamp;
    private String ApiClientId;
    private String TenantId;
    private Boolean IsScheduler;
    private String ConnectId;
    
    public String getCleanInventoryNumber() {
        if (resourceId == null || resourceId.trim().isEmpty()) {
            return null;
        }
        return resourceId.replace("+", "");
    }
    
    public boolean isNewInventoryItem() {
        return "CREATED".equals(action);
    }
} 
