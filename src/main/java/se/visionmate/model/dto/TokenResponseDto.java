package se.visionmate.model.dto;

import io.micronaut.serde.annotation.Serdeable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Serdeable
public class TokenResponseDto {
    private String access_token;
    private Integer expires_in;
    private String token_type;
    private String scope;
}