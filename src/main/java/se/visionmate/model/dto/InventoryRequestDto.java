package se.visionmate.model.dto;

import io.micronaut.serde.annotation.Serdeable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Serdeable
public class InventoryRequestDto {
    private Integer pageSize;
    private Integer pageNumber;
    private String inventoryNumber;
    
    public boolean isPaginationRequest() {
        return pageSize != null && pageNumber != null;
    }
    
    public boolean isSingleProductRequest() {
        return inventoryNumber != null && !inventoryNumber.trim().isEmpty();
    }
} 