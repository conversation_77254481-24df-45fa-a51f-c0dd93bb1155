package se.visionmate.model.dto;

import io.micronaut.serde.annotation.Serdeable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Serdeable
public class SftpResponseDto {
    private String status;
    private String message;
    private Integer filesProcessed;
    private Integer successCount;
    private Integer errorCount;
} 