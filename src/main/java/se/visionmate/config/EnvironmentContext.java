package se.visionmate.config;

import io.micronaut.context.env.Environment;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

/**
 * Application environment context information.
 * Centralizes environment-related checks.
 */
@Singleton
@RequiredArgsConstructor
public class EnvironmentContext {

    private final Environment environment;

    public boolean isDevelopment() {
        return environment.getActiveNames().contains("dev");
    }

    public boolean isProduction() {
        return environment.getActiveNames().contains("prod");
    }

    public java.util.Set<String> getActiveNames() {
        return environment.getActiveNames();
    }

    public boolean isProfileActive(String profileName) {
        return environment.getActiveNames().contains(profileName);
    }
}