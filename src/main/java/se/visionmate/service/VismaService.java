package se.visionmate.service;

import io.micronaut.context.annotation.Value;
import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.client.HttpClient;
import io.micronaut.http.uri.UriBuilder;
import io.micronaut.json.JsonMapper;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.config.EnvironmentContext;
import se.visionmate.model.dto.TokenResponseDto;
import se.visionmate.model.dto.InventoryNotificationDto;
import se.visionmate.model.visma.InventoryItem;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class VismaService {

    private final EnvironmentContext environmentContext;
    private final TokenService tokenService;
    private final JsonMapper objectMapper;
    private final HttpClient httpClient;

    @Value("${visma.api.url}")
    private String apiUrl;

    @Value("${visma.api.expand}")
    private String apiExpand;

    @Value("${visma.api.timeout}")
    private int apiTimeout;

    /**
     * Get inventory items with pagination
     * @param pageSize as integer
     * @param pageNumber as integer
     * @return list of InventoryItem or throws exception if error
     */
    public List<InventoryItem> getInventoryItems(Integer pageSize, Integer pageNumber) {
        try {
            log.info("Fetching inventory items from Visma.net API with pagination: pageSize={}, pageNumber={}", pageSize, pageNumber);
            TokenResponseDto tokenResponse = tokenService.getToken();
            String token = tokenResponse.getAccess_token();

            if (environmentContext.isDevelopment()) {
                log.info("Fetched access token for Visma.net API: {}", token);
            }

            UriBuilder uriBuilder = UriBuilder.of(apiUrl);
            
            if (pageSize != null) {
                uriBuilder.queryParam("$top", pageSize);
            }
            if (pageNumber != null && pageSize != null) {
                uriBuilder.queryParam("pageNumber", pageNumber);
                uriBuilder.queryParam("pageSize", pageSize);
                uriBuilder.queryParam("inventoryTypes", "FinishedGoodItem");
            }
            
            URI uri = uriBuilder.build();

            HttpRequest<?> request = HttpRequest.GET(uri)
                    .header("Authorization", "Bearer " + token)
                    .header("Accept", "application/json")
                    .header("Content-Type", "application/json");

            HttpResponse<String> response = httpClient.toBlocking().exchange(request, String.class);

            if (response.getStatus().getCode() != 200) {
                log.error("Error fetching inventory items. Status code: {}, Body: {}", 
                          response.getStatus().getCode(), response.getBody().orElse(""));
                throw new RuntimeException("Error fetching inventory items from Visma.net");
            }

            String body = response.getBody().orElseThrow(() -> 
                new RuntimeException("Empty response body from Visma.net API"));
            return objectMapper.readValue(body, Argument.listOf(InventoryItem.class));
        } catch (IOException e) {
            log.error("Error fetching inventory items from Visma.net", e);
            throw new RuntimeException("Error fetching inventory items from Visma.net", e);
        }
    }

    /**
     * Get all inventory items
     * @return list of InventoryItem or throws exception if error
     */
    public List<InventoryItem> getInventoryItems() {
        return getInventoryItems(null, null);
    }

    /**
     * Get a single inventory item by inventory number
     * @param inventoryNumber as string
     * @return list of InventoryItem or throws exception if error
     */
    public List<InventoryItem> getInventoryItem(String inventoryNumber) {
        try {
            log.info("Fetching single inventory item from Visma.net API: {}", inventoryNumber);
            TokenResponseDto tokenResponse = tokenService.getToken();
            String token = tokenResponse.getAccess_token();

            if (environmentContext.isDevelopment()) {
                log.info("Fetched access token for Visma.net API: {}", token);
            }

            URI uri = UriBuilder.of(apiUrl)
                    .path("/" + inventoryNumber)
                    .build();

            HttpRequest<?> request = HttpRequest.GET(uri)
                    .header("Authorization", "Bearer " + token)
                    .header("Accept", "application/json")
                    .header("Content-Type", "application/json");

            HttpResponse<String> response = httpClient.toBlocking().exchange(request, String.class);

            if (response.getStatus().getCode() != 200) {
                log.error("Error fetching inventory item. Status code: {}, Body: {}", 
                          response.getStatus().getCode(), response.getBody().orElse(""));
                throw new RuntimeException("Error fetching inventory item from Visma.net");
            }

            String body = response.getBody().orElseThrow(() -> 
                new RuntimeException("Empty response body from Visma.net API"));
            
            InventoryItem singleItem = objectMapper.readValue(body, InventoryItem.class);
            return Collections.singletonList(singleItem);
        } catch (IOException e) {
            log.error("Error fetching inventory item from Visma.net", e);
            throw new RuntimeException("Error fetching inventory item from Visma.net", e);
        }
    }

    /**
     * @param notifications as list of inventory notifications to process
     * @return list of InventoryItem 
     */
    public List<InventoryItem> processNotifications(List<InventoryNotificationDto> notifications) {
        List<String> createdInventoryNumbers = notifications.stream()
            .filter(InventoryNotificationDto::isNewInventoryItem)
            .map(InventoryNotificationDto::getCleanInventoryNumber)
            .filter(inventoryNumber -> inventoryNumber != null && !inventoryNumber.trim().isEmpty())
            .collect(Collectors.toList());

        log.info("Found {} new inventory item notifications with valid inventory numbers: {}", 
                createdInventoryNumbers.size(), createdInventoryNumbers);

        if (createdInventoryNumbers.isEmpty()) {
            log.warn("No valid new inventory item notifications found, returning empty list");
            return new ArrayList<>();
        }

        List<InventoryItem> allInventoryItems = new ArrayList<>();
        for (String inventoryNumber : createdInventoryNumbers) {
            try {
                log.info("Fetching inventory item for notification: {}", inventoryNumber);
                List<InventoryItem> items = getInventoryItem(inventoryNumber);
                allInventoryItems.addAll(items);
                log.info("Successfully fetched {} items for inventory number: {}", items.size(), inventoryNumber);
            } catch (Exception e) {
                log.error("Failed to fetch inventory item for notification: {}", inventoryNumber, e);
            }
        }

        log.info("Total inventory items fetched from notifications: {}", allInventoryItems.size());
        return allInventoryItems;
    }
}
