package se.visionmate.service;

import io.micronaut.context.annotation.Value;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.config.EnvironmentContext;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Singleton
public class EmailService {

    @Value("${aws.ses.region}")
    private String awsRegion;

    @Value("${aws.ses.access-key-id:}")
    private String awsAccessKeyId;

    @Value("${aws.ses.secret-access-key:}")
    private String awsSecretAccessKey;

    @Value("${email.from}")
    private String fromEmail;

    @Value("${email.to}")
    private String toEmail;

    @Value("${email.subject.prefix:ELISA Part Sync Error}")
    private String subjectPrefix;

    @Value("${email.enabled:true}")
    private Boolean emailEnabled;

    @Inject
    private EnvironmentContext environmentContext;

    private SesClient sesClient;

    private SesClient getSesClient() {
        if (sesClient == null) {
            if (environmentContext.isDevelopment()) {
                log.info("Initializing SES client with static credentials for development environment");
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(
                        awsAccessKeyId,
                        awsSecretAccessKey
                );

                sesClient = SesClient.builder()
                        .region(Region.of(awsRegion))
                        .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                        .build();
            } else {
                log.info("Initializing SES client with default credentials for production environment");
                sesClient = SesClient.builder()
                        .region(Region.of(awsRegion))
                        .build();
            }
        }
        return sesClient;
    }

    public void sendErrorNotification(String errorMessage, Exception exception) {
        if (!emailEnabled) {
            log.info("Email notifications are disabled, skipping error notification");
            return;
        }

        try {
            String subject = String.format("%s - %s", subjectPrefix, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            String body = buildEmailBody(errorMessage, exception);

            SendEmailRequest emailRequest = SendEmailRequest.builder()
                    .destination(Destination.builder()
                            .toAddresses(toEmail)
                            .build())
                    .message(Message.builder()
                            .subject(Content.builder()
                                    .charset("UTF-8")
                                    .data(subject)
                                    .build())
                            .body(Body.builder()
                                    .text(Content.builder()
                                            .charset("UTF-8")
                                            .data(body)
                                            .build())
                                    .build())
                            .build())
                    .source(fromEmail)
                    .build();

            SendEmailResponse response = getSesClient().sendEmail(emailRequest);
            log.info("Error notification email sent successfully. MessageId: {}", response.messageId());

        } catch (Exception e) {
            log.error("Failed to send error notification email", e);
        }
    }

    public void sendErrorNotification(String errorMessage) {
        sendErrorNotification(errorMessage, null);
    }

    private String buildEmailBody(String errorMessage, Exception exception) {
        StringBuilder body = new StringBuilder();
        body.append("An error occurred in the ELISA Part Sync system.\n\n");
        body.append("Timestamp: ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n");
        body.append("Environment: ").append(environmentContext.getActiveNames()).append("\n\n");
        body.append("Error Message:\n").append(errorMessage).append("\n\n");

        if (exception != null) {
            body.append("Exception Details:\n");
            body.append("Type: ").append(exception.getClass().getSimpleName()).append("\n");
            body.append("Message: ").append(exception.getMessage()).append("\n");
            
            if (exception.getCause() != null) {
                body.append("Caused by: ").append(exception.getCause().getClass().getSimpleName());
                if (exception.getCause().getMessage() != null) {
                    body.append(" - ").append(exception.getCause().getMessage());
                }
                body.append("\n");
            }
            
            body.append("\nStack Trace:\n");
            for (StackTraceElement element : exception.getStackTrace()) {
                body.append(element.toString()).append("\n");
            }
        }

        return body.toString();
    }
}
