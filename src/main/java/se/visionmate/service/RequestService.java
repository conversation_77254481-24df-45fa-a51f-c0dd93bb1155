package se.visionmate.service;

import io.micronaut.json.JsonMapper;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.model.dto.InventoryRequestDto;
import se.visionmate.model.dto.InventoryNotificationDto;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Singleton
public class RequestService {

    @Inject
    private JsonMapper objectMapper;

    /**
     * @param body as request body string
     * @return parsed InventoryRequestDto or default instance if parsing fails
     */
    public InventoryRequestDto parseRequestBody(String body) {
        if (body == null || body.trim().isEmpty()) {
            return new InventoryRequestDto();
        }

        try {
            return objectMapper.readValue(body, InventoryRequestDto.class);
        } catch (IOException e) {
            log.warn("Unable to parse request body, using default behavior: {}", e.getMessage());
            return new InventoryRequestDto();
        }
    }

    /**
     * @param body as request body string
     * @return true if the body appears to be a notification payload
     */
    public boolean isNotificationPayload(String body) {
        if (body == null || body.trim().isEmpty()) {
            return false;
        }
        
        String trimmedBody = body.trim();
        return trimmedBody.startsWith("[") && 
               trimmedBody.contains("notificationId") && 
               trimmedBody.contains("action") && 
               trimmedBody.contains("resourceId");
    }

    /**
     * @param body as request body string 
     * @return list of parsed notifications or empty list if parsing fails
     */
    public List<InventoryNotificationDto> parseNotificationPayload(String body) {
        try {
            InventoryNotificationDto[] notificationArray = objectMapper.readValue(body, InventoryNotificationDto[].class);
            return List.of(notificationArray);
        } catch (IOException e) {
            log.error("Failed to parse notification payload", e);
            return new ArrayList<>();
        }
    }
} 