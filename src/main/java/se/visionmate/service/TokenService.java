package se.visionmate.service;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.lambda.LambdaClient;
import software.amazon.awssdk.services.lambda.LambdaClientBuilder;
import software.amazon.awssdk.services.lambda.model.InvokeRequest;
import software.amazon.awssdk.services.lambda.model.InvokeResponse;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.type.Argument;
import io.micronaut.json.JsonMapper;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.config.EnvironmentContext;
import se.visionmate.model.dto.TokenRequestDto;
import se.visionmate.model.dto.TokenResponseDto;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for obtaining authentication tokens from the Visma.net API via AWS Lambda.
 * This service can be used for both local development and production environments.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class TokenService {

    private final EnvironmentContext environmentContext;
    private final JsonMapper objectMapper;

    @Value("${token.lambda.region}")
    private String awsRegion;

    @Value("${token.lambda.profile}")
    private String awsProfile;

    @Value("${token.lambda.access-key-id}")
    private String accessKeyId;

    @Value("${token.lambda.secret-access-key}")
    private String secretAccessKey;

    @Value("${token.lambda.function-name}")
    private String lambdaFunctionName;

    @Value("${visma.auth.client-id}")
    private String clientId;

    @Value("${visma.auth.client-secret}")
    private String clientSecret;

    @Value("${visma.auth.tenant-id}")
    private String tenantId;

    @Value("${visma.auth.scope}")
    private String scope;

    /**
     * @return TokenResponseDto or throws exception if error
     */
    public TokenResponseDto getToken() {

        // DEBUG: Log all class variables while running locally
        if (environmentContext.isDevelopment()) {
            log.info("=== DEBUG: ALL TOKEN-SERVICE VARIABLES ===");
            log.info("awsRegion: {}", awsRegion);
            log.info("awsProfile: {}", awsProfile);
            log.info("accessKeyId: {}", accessKeyId);
            log.info("secretAccessKey: {}", secretAccessKey);
            log.info("lambdaFunctionName: {}", lambdaFunctionName);
            log.info("clientId: {}", clientId);
            log.info("clientSecret: {}", clientSecret);
            log.info("tenantId: {}", tenantId);
            log.info("scope: {}", scope);
            log.info("=== END TOKEN-SERVICE VARIABLES ===");
        }

        try {
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("httpMethod", "POST");
            requestPayload.put("path", "/token");

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            requestPayload.put("headers", headers);

            TokenRequestDto tokenRequest = TokenRequestDto.builder()
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .tenantId(tenantId)
                    .scope(scope)
                    .build();

            requestPayload.put("body", objectMapper.writeValueAsString(tokenRequest));         
            
            String payloadJson = objectMapper.writeValueAsString(requestPayload);

            // Debug: Log the payload
            if (environmentContext.isDevelopment()) {
                log.info("Invoking Lambda function: {}, region: {} using AWS CLI", lambdaFunctionName, awsRegion);
                log.info("DEBUG - Payload being sent: {}", payloadJson);
            }

            LambdaClientBuilder lambdaClientBuilder = LambdaClient.builder();

            if (environmentContext.isDevelopment()) {
                log.info("Using visionmate static credentials directly");
                // String accessKeyId = "********************";
                // String secretAccessKey = "zWIjEi7IouifxSTpUmGjnkchI8zF0M8U8NquFIkf";
            
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);
                lambdaClientBuilder.credentialsProvider(StaticCredentialsProvider.create(awsCredentials));
            }

            LambdaClient lambdaClient = lambdaClientBuilder
                    .region(Region.of(awsRegion))
                    .build();
            
            InvokeRequest invokeRequest = InvokeRequest.builder()
                    .functionName(lambdaFunctionName)
                    .payload(SdkBytes.fromUtf8String(payloadJson))
                    .build();
            
            InvokeResponse invokeResponse = lambdaClient.invoke(invokeRequest);
            
            if (invokeResponse.functionError() != null) {
                log.error("Lambda function error: {}", invokeResponse.functionError());
                throw new RuntimeException("Error invoking token Lambda function");
            }
            
            String responseJson = invokeResponse.payload().asUtf8String();
            Map<String, Object> response = objectMapper.readValue(responseJson, Argument.mapOf(String.class, Object.class));

            if (response.containsKey("statusCode") && !response.get("statusCode").equals(200)) {
                log.error("Failed to get token. Status code: {}, Body: {}", response.get("statusCode"), response.get("body"));
                throw new RuntimeException("Failed to get token from token service");
            }

            String responseBody = (String) response.get("body");
            return objectMapper.readValue(responseBody, TokenResponseDto.class);

        } catch (IOException e) {
            log.error("Error getting token", e);
            throw new RuntimeException("Error getting token", e);
        }
    }
}
