package se.visionmate.service;

import io.micronaut.context.annotation.Value;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.config.EnvironmentContext;
import se.visionmate.exception.NoValidPartsException;
import se.visionmate.model.ValidationError;
import se.visionmate.model.visma.InventoryItem;
import se.visionmate.model.xml.*;
import se.visionmate.service.EmailService;

import java.io.StringWriter;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;

@Slf4j
@Singleton
public class XmlService {

    @Value("${xml.sender.logical-id}")
    private String senderLogicalId;

    @Value("${xml.sender.confirmation}")
    private String senderConfirmation;

    @Value("${xml.sync.action-criteria}")
    private String syncActionCriteria;

    @Value("${xml.part.revision}")
    private String partRevision;

    @Value("${xml.part.division}")
    private String partDivision;

    @Value("${xml.part.ean-code-type}")
    private String eanCodeType;

    @Inject
    private Validator validator;

    @Inject
    private EmailService emailService;

    @Inject
    private EnvironmentContext environmentContext;

    public String generateInventoryXml(List<InventoryItem> inventoryItems) {
        try {
            PartSync partSync = new PartSync();

            ControlArea controlArea = new ControlArea();
        
            Sender sender = new Sender();
            sender.setLogicalId(senderLogicalId);
            sender.setConfirmation(senderConfirmation);
            controlArea.setSender(sender);
            controlArea.setCreationDateTime(formatCreationDateTime());
            
            // note => ControlArea/ReferenceId/Id (can be empty)
            ReferenceId referenceId = new ReferenceId();
            if (!inventoryItems.isEmpty() && inventoryItems.get(0).getNote() != null && !inventoryItems.get(0).getNote().trim().isEmpty()) {
                referenceId.setId(inventoryItems.get(0).getNote());
            } else {
                referenceId.setId(""); // can be empty by convention
            }
            controlArea.setReferenceId(referenceId);

            partSync.setControlArea(controlArea);

            DataArea dataArea = new DataArea();
            
            Sync sync = new Sync();
            sync.getActionCriteria().setAction(syncActionCriteria);
            dataArea.setSync(sync);

            List<Part> parts = new ArrayList<>();
            List<ValidationError> validationErrors = new ArrayList<>();
            int validParts = 0;
            int invalidParts = 0;
            
            for (InventoryItem item : inventoryItems) {
                Part part = mapInventoryItemToPart(item);
                
                Set<ConstraintViolation<Part>> violations = validator.validate(part);
                if (violations.isEmpty()) {
                    parts.add(part);
                    validParts++;
                    log.debug("Part for inventory item '{}' passed validation", item.getInventoryNumber());
                } else {
                    // Collect validation error information for batch email
                    validationErrors.add(new ValidationError(item, violations));
                    invalidParts++;
                    log.error("Validation failure for item '{}': {} violations", item.getInventoryNumber(), violations.size());
                }
            }
            
            // Send single summary email with all validation errors (if any)
            if (!validationErrors.isEmpty()) {
                sendValidationErrorsSummaryEmail(validationErrors);
            }
            
            log.info("Part validation summary: {} valid parts included, {} invalid parts excluded", validParts, invalidParts);
            
            // Check if no valid parts found
            if (parts.isEmpty()) {
                log.warn("No valid parts found after validation - throwing NoValidPartsException");
                throw new NoValidPartsException(inventoryItems.size());
            }
            
            dataArea.setParts(parts);

            partSync.setDataArea(dataArea);

            JAXBContext context = JAXBContext.newInstance(PartSync.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

            StringWriter writer = new StringWriter();
            marshaller.marshal(partSync, writer);

            return writer.toString();
        } catch (JAXBException e) {
            log.error("Error generating XML", e);
            throw new RuntimeException("Error generating XML", e);
        }
    }

    private Part mapInventoryItemToPart(InventoryItem item) {
        Part part = new Part();

        Part.PartId partId = new Part.PartId();
        partId.setId(item.getInventoryNumber());
        partId.setRevision(partRevision);
        partId.setDivision(partDivision);
        part.setPartId(partId);

        Part.EANCodeArea eanCodeArea = new Part.EANCodeArea();
        Part.EANCodeArea.SingleUnit singleUnit = new Part.EANCodeArea.SingleUnit();
        Part.EANCodeArea.SingleUnit.EANCode eanCode = new Part.EANCodeArea.SingleUnit.EANCode();
        Part.EANCodeArea.SingleUnit.EANCode.EANCodeId eanCodeId = new Part.EANCodeArea.SingleUnit.EANCode.EANCodeId();

        // crossReferences[EAN].alternateId => DataArea/Part/EANCodeArea/SingleUnit/EANCode/EANCodeId/Id
        if (item.getCrossReferences() != null && !item.getCrossReferences().isEmpty()) {
            for (InventoryItem.CrossReference ref : item.getCrossReferences()) {
                if ("Barcode".equalsIgnoreCase(ref.getAlternateType())) {
                    eanCodeId.setId(ref.getAlternateID());
                    eanCodeId.setType(eanCodeType);
                    break;
                }
            }
        }

        // Set default values if EAN not found
        if (eanCodeId.getId() == null) {
            eanCodeId.setId("");
            eanCodeId.setType("");
        }

        eanCode.setEanCodeId(eanCodeId);
        singleUnit.setEanCode(eanCode);
        eanCodeArea.setSingleUnit(singleUnit);
        part.setEanCodeArea(eanCodeArea);

        part.setDescription(item.getDescription());
        part.setUnitType(item.getBaseUnit());

        if (item.getLotSerialClass() != null) {
            part.setSerialNumberLevel(item.getLotSerialClass().getId());
        }

        if (item.getLotSerialClass() != null) {
            part.setLotCreationCode(item.getLotSerialClass().getDescription());
        }
        
        if (item.getItemClass() != null) {
            part.setManufacturerProductCode(item.getItemClass().getId());
        }

        return part;
    }

    private void sendValidationErrorsSummaryEmail(List<ValidationError> validationErrors) {
        StringBuilder emailBody = new StringBuilder();
        emailBody.append("Part validation failures occurred during inventory sync process.\n\n");
        emailBody.append("Summary: ").append(validationErrors.size()).append(" part(s) failed validation and were excluded from XML output.\n\n");
        emailBody.append("Failed Parts Details:\n");
        emailBody.append("=".repeat(50)).append("\n\n");
        
        for (int i = 0; i < validationErrors.size(); i++) {
            ValidationError error = validationErrors.get(i);
            InventoryItem item = error.getItem();
            Set<ConstraintViolation<Part>> violations = error.getViolations();
            
            emailBody.append(String.format("%d. Inventory Item: %s\n", i + 1, item.getInventoryNumber()));
            emailBody.append("   Validation Violations:\n");
            
            for (ConstraintViolation<Part> violation : violations) {
                String propertyPath = violation.getPropertyPath().toString();
                String violationMessage = violation.getMessage();
                Object invalidValue = violation.getInvalidValue();
                
                log.error("Validation violation for item '{}' field '{}': {} (value: '{}')", 
                    item.getInventoryNumber(), propertyPath, violationMessage, invalidValue);
                
                emailBody.append("   - Field: ").append(propertyPath).append("\n");
                emailBody.append("     Error: ").append(violationMessage).append("\n");
                emailBody.append("     Invalid value: ").append(invalidValue).append("\n");
            }
            emailBody.append("\n");
        }
        
        emailBody.append("Action Required: Please review and correct the data for these inventory items.\n");
        emailBody.append("These parts have been excluded from the current XML sync.");
        
        // Send single summary email
        emailService.sendErrorNotification(emailBody.toString());
        
        log.warn("Sent validation errors summary email for {} failed parts", validationErrors.size());
    }

    private String formatCreationDateTime() {
        return Instant.now().truncatedTo(ChronoUnit.SECONDS)
                .atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
    }
}
