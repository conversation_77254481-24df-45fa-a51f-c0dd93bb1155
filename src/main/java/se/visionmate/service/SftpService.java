package se.visionmate.service;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.lambda.LambdaClient;
import software.amazon.awssdk.services.lambda.LambdaClientBuilder;
import software.amazon.awssdk.services.lambda.model.InvokeRequest;
import software.amazon.awssdk.services.lambda.model.InvokeResponse;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.type.Argument;
import io.micronaut.json.JsonMapper;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import se.visionmate.config.EnvironmentContext;
import se.visionmate.model.dto.SftpFileDto;
import se.visionmate.model.dto.SftpRequestDto;
import se.visionmate.model.dto.SftpResponseDto;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for uploading files via SFTP using AWS Lambda.
 * This service can be used for both local development and production environments.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class SftpService {

    private final EnvironmentContext environmentContext;
    private final JsonMapper objectMapper;

    @Value("${sftp.lambda.region}")
    private String awsRegion;

    @Value("${sftp.lambda.access-key-id}")
    private String accessKeyId;

    @Value("${sftp.lambda.secret-access-key}")
    private String secretAccessKey;

    @Value("${sftp.lambda.function-name}")
    private String lambdaFunctionName;

    @Value("${sftp.lambda.request.http-method}")
    private String requestHttpMethod;

    @Value("${sftp.lambda.request.path}")
    private String requestPath;

    @Value("${sftp.lambda.request.content-type}")
    private String requestContentType;

    @Value("${sftp.folder.path}")
    private String folderPath;

    @Value("${sftp.create.folders}")
    private boolean createFolders;

    public SftpResponseDto uploadFile(String fileName, String xmlContent) {
        
        // DEBUG: Log all class variables while running locally
        if (environmentContext.isDevelopment()) {
            log.info("=== DEBUG: ALL SFTP-SERVICE VARIABLES ===");
            log.info("awsRegion: {}", awsRegion);
            log.info("accessKeyId: {}", accessKeyId);
            log.info("secretAccessKey: {}", secretAccessKey);
            log.info("lambdaFunctionName: {}", lambdaFunctionName);
            log.info("fileName: {}", fileName);
            log.info("xmlContent length: {}", xmlContent != null ? xmlContent.length() : 0);
            log.info("=== END SFTP-SERVICE VARIABLES ===");
        }

        try {
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("httpMethod", requestHttpMethod);
            requestPayload.put("path", requestPath);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", requestContentType);
            requestPayload.put("headers", headers);

            SftpFileDto fileDto = SftpFileDto.builder()
                    .fileName(fileName)
                    .fileContent(xmlContent)
                    .folderPath(folderPath)
                    .build();

            SftpRequestDto sftpRequest = SftpRequestDto.builder()
                    .files(Collections.singletonList(fileDto))
                    .createFolders(createFolders)
                    .build();

            requestPayload.put("body", objectMapper.writeValueAsString(sftpRequest));         
            
            String payloadJson = objectMapper.writeValueAsString(requestPayload);

            // Debug: Log the payload
            if (environmentContext.isDevelopment()) {
                log.info("Invoking SFTP Lambda function: {}, region: {}", lambdaFunctionName, awsRegion);
                log.info("DEBUG - Payload being sent: {}", payloadJson);
            }

            LambdaClientBuilder lambdaClientBuilder = LambdaClient.builder();

            if (environmentContext.isDevelopment()) {
                log.info("Using static credentials for SFTP Lambda");
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);
                lambdaClientBuilder.credentialsProvider(StaticCredentialsProvider.create(awsCredentials));
            }

            LambdaClient lambdaClient = lambdaClientBuilder
                    .region(Region.of(awsRegion))
                    .build();
            
            InvokeRequest invokeRequest = InvokeRequest.builder()
                    .functionName(lambdaFunctionName)
                    .payload(SdkBytes.fromUtf8String(payloadJson))
                    .build();
            
            InvokeResponse invokeResponse = lambdaClient.invoke(invokeRequest);
            
            if (invokeResponse.functionError() != null) {
                log.error("SFTP Lambda function error: {}", invokeResponse.functionError());
                throw new RuntimeException("Error invoking SFTP Lambda function");
            }
            
            String responseJson = invokeResponse.payload().asUtf8String();
            Map<String, Object> response = objectMapper.readValue(responseJson, Argument.mapOf(String.class, Object.class));

            if (response.containsKey("statusCode") && !response.get("statusCode").equals(200)) {
                log.error("Failed to upload file via SFTP. Status code: {}, Body: {}", response.get("statusCode"), response.get("body"));
                throw new RuntimeException("Failed to upload file via SFTP Lambda function");
            }

            String responseBody = (String) response.get("body");
            return objectMapper.readValue(responseBody, SftpResponseDto.class);

        } catch (IOException e) {
            log.error("Error invoking SFTP Lambda function", e);
            throw new RuntimeException("Error invoking SFTP Lambda function", e);
        }
    }
} 