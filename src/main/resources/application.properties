# Common Configuration for All Environments

# Application
micronaut.application.name=cenor-elisa-part-sync

# Active profiles
# Use -Dmicronaut.environments=dev for development
# Use -Dmicronaut.environments=prod for production
# micronaut.environments=dev

# HTTP Client Configuration
micronaut.http.client.pool.enabled=true
micronaut.http.client.pool.max-connections=50
micronaut.http.client.read-timeout=30s
micronaut.http.client.connect-timeout=15s
micronaut.http.client.max-content-length=20971520

# Connection pool settings
micronaut.http.client.pool.acquire-timeout=45s
micronaut.http.client.pool.max-pending-acquires=5

# Token Lambda Configuration
token.lambda.region=eu-north-1
token.lambda.profile=cenor
token.lambda.function-name=visma-token-manager

# SFTP Lambda Configuration
sftp.lambda.region=eu-north-1
sftp.lambda.function-name=elisa-ftp-watcher
sftp.lambda.request.http-method=POST
sftp.lambda.request.path=/ftp-watcher/upload
sftp.lambda.request.content-type=application/json

# SFTP Configuration
sftp.folder.path=/to_hub_test/PartSync
sftp.create.folders=true

# Visma API Configuration
visma.api.url=https://api.finance.visma.net/v1/inventory
visma.api.expand=itemClass,crossReferences,unitConversions
visma.api.timeout=10

# XML Mapping - Fixed Values
xml.sender.logical-id=CENOR
xml.sender.confirmation=1
xml.sync.action-criteria=Sync
xml.part.revision=C01
xml.part.division=CENOR
xml.part.ean-code-type=1

# Email Configuration
aws.ses.region=eu-north-1
email.subject.prefix=ELISA Part Sync Error
email.from=<EMAIL>
