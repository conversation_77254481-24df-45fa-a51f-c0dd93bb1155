# Production Environment Configuration

# Application
# micronaut.application.name=cenor-elisa-part-sync

# SFTP Lambda Credentials (replace with actual values)
sftp.lambda.access-key-id=sftp_access_key_id
sftp.lambda.secret-access-key=sftp_secret_access_key

# Visma Token Authentication
visma.auth.client-id=client_id
visma.auth.client-secret=client_secret
visma.auth.tenant-id=tenant_id
visma.auth.scope=vismanet_erp_service_api:read vismanet_erp_service_api:create vismanet_erp_service_api:update vismanet_erp_service_api:delete

# Response Configuration
response.content-type=application/json

# Email Configuration (Production)
email.enabled=true
email.to=<EMAIL>
