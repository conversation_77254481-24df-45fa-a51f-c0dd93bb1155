# Development Environment Configuration

# Application
# micronaut.application.name=cenor-elisa-part-sync

# Token Lambda Credentials
token.lambda.access-key-id=********************
token.lambda.secret-access-key=ssFQ19WxSKkvnmlyL+Q5r4Z5JDqATlb7uvhds8oc

# SFTP Lambda Credentials
sftp.lambda.access-key-id=********************
sftp.lambda.secret-access-key=ssFQ19WxSKkvnmlyL+Q5r4Z5JDqATlb7uvhds8oc

# Visma Token Authentication
visma.auth.client-id=isv_cenor
visma.auth.client-secret=kgP6xWktvxupt6DlIxi7867yZyuvUPUb6F92Ru3V5nNse5TLkNmfFtZE6sZQsrCb
visma.auth.tenant-id=24a02029-c475-11ec-b60b-0638767d04b5
visma.auth.scope=vismanet_erp_service_api:read vismanet_erp_service_api:create vismanet_erp_service_api:update vismanet_erp_service_api:delete

# Response Configuration
response.content-type=application/xml

# Email Configuration
email.enabled=true
email.to=<EMAIL>
aws.ses.access-key-id=********************
aws.ses.secret-access-key=ssFQ19WxSKkvnmlyL+Q5r4Z5JDqATlb7uvhds8oc
