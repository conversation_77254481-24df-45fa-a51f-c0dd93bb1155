# Cenor Elisa Part Sync Lambda Function

A Micronaut-based AWS Lambda function that fetches inventory data from Visma.net ERP system and converts it to XML format.

## What it does

- Fetches inventory items from Visma.net ERP using OAuth2 authentication
- Transforms JSON data to standardized XML format (PartSync schema)
- Transfer the generated XML file to HUB Logistics via SFTP

## Prerequisites

- **Java 21**
- **AWS SAM CLI** - [Installation Guide](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html)

## Running Locally

```bash
# Make script executable (first time only)
chmod +x run-local.sh

# Start local development server
./run-local.sh
```

The API will be available at: `http://localhost:3000/cenor-elisa-part-sync`

Test with:
```bash
curl http://localhost:3000/cenor-elisa-part-sync
```

## Required Files

### Development Files
- **`dev-template.yaml`** - SAM template for local development
- **`dev-env.json`** - Environment variables for local execution
- **`run-local.sh`** - Local development script

### Configuration Files
- **`src/main/resources/application.properties`** - Common configuration
- **`src/main/resources/application-dev.properties`** - Development settings
- **`src/main/resources/application-prod.properties`** - Production settings

## Building

```bash
# Build the project
./gradlew clean shadowJar
```
