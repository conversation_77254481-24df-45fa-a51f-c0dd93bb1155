plugins {
    id("io.micronaut.library") version "4.5.3"
    id("com.gradleup.shadow") version "8.3.6"
}

version = "$appVersion"
group = "se.visionmate"

repositories {
    mavenCentral()
}

dependencies {
    annotationProcessor("io.micronaut:micronaut-inject-java")
    annotationProcessor("io.micronaut.serde:micronaut-serde-processor")
    annotationProcessor("org.projectlombok:lombok")
    implementation("com.amazonaws:aws-lambda-java-events")
    implementation("io.micronaut.aws:micronaut-aws-lambda-events-serde")
    implementation("io.micronaut.aws:micronaut-function-aws")
    implementation("io.micronaut.crac:micronaut-crac")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("org.projectlombok:lombok")
    implementation("io.micronaut:micronaut-http-client")
    implementation("jakarta.xml.bind:jakarta.xml.bind-api:4.0.0")
    implementation("org.glassfish.jaxb:jaxb-runtime:4.0.2")
    implementation("jakarta.validation:jakarta.validation-api:3.1.1")
    implementation("io.micronaut.validation:micronaut-validation")
    runtimeOnly("org.hibernate.validator:hibernate-validator:8.0.1.Final")
    implementation("software.amazon.awssdk:lambda:2.31.63")
    implementation("software.amazon.awssdk:auth:2.31.63")
    implementation("software.amazon.awssdk:ses:2.31.63")
    runtimeOnly("ch.qos.logback:logback-classic")
}


java {
    sourceCompatibility = JavaVersion.toVersion("21")
    targetCompatibility = JavaVersion.toVersion("21")
}


micronaut {
    runtime("lambda_java")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("se.visionmate.*")
    }
}

def buildEnv = System.getProperty('buildEnv')
shadowJar {
    archiveClassifier = ['prod', 'dev'].contains(buildEnv) ? buildEnv : 'all'
}

// Custom task to print version (for Jenkins CI/CD)
task versionName {
    doLast {
        println version
    }
}
